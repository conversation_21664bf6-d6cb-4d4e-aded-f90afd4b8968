// streaming_batcher.rs
// Memory-efficient streaming batcher for massive datasets

use candle_core::{<PERSON><PERSON>, Result, Tensor};
use polars::prelude::*;
use std::path::Path;

/// Streaming batcher that loads data chunks on-demand to avoid memory overflow
pub struct StreamingBatcher {
    file_paths: Vec<String>,
    sequence_length: usize,
    batch_size: usize,
    device: Device,
    
    // Current state
    current_file_idx: usize,
    current_position_in_file: usize,
    current_data_chunk: Option<Tensor>,
    chunk_size: usize, // Number of timesteps to load at once
    
    // Dataset info
    num_features: usize,
    total_sequences_processed: usize,
}

impl StreamingBatcher {
    /// Create a new streaming batcher
    pub fn new(
        file_paths: Vec<String>,
        sequence_length: usize,
        batch_size: usize,
        device: Device,
        chunk_size: usize, // e.g., 50000 timesteps per chunk
    ) -> Result<Self> {
        // Determine number of features from first file
        let num_features = if !file_paths.is_empty() {
            Self::get_num_features(&file_paths[0])?
        } else {
            return Err(candle_core::Error::Msg("No files provided".to_string()));
        };
        
        println!("StreamingBatcher initialized:");
        println!("  Files: {}", file_paths.len());
        println!("  Sequence length: {}", sequence_length);
        println!("  Batch size: {}", batch_size);
        println!("  Chunk size: {} timesteps", chunk_size);
        println!("  Features: {}", num_features);
        
        Ok(Self {
            file_paths,
            sequence_length,
            batch_size,
            device,
            current_file_idx: 0,
            current_position_in_file: 0,
            current_data_chunk: None,
            chunk_size,
            num_features,
            total_sequences_processed: 0,
        })
    }
    
    /// Get number of features from a parquet file
    pub fn get_num_features(file_path: &str) -> Result<usize> {
        if !Path::new(file_path).exists() {
            return Err(candle_core::Error::Msg(format!("File does not exist: {}", file_path)));
        }
        
        let df = LazyFrame::scan_parquet(file_path, Default::default())
            .limit(1)
            .collect()
            .map_err(|e| candle_core::Error::Msg(format!("Failed to scan parquet file: {}", e)))?;
        
        // Filter out timestamp column if present
        let feature_columns: Vec<String> = df.get_column_names()
            .iter()
            .filter(|&name| !name.to_lowercase().contains("timestamp"))
            .map(|&name| name.to_string())
            .collect();
        
        Ok(feature_columns.len())
    }
    
    /// Load a chunk of data from the current file
    fn load_next_chunk(&mut self) -> Result<bool> {
        if self.current_file_idx >= self.file_paths.len() {
            return Ok(false); // No more files
        }
        
        let file_path = &self.file_paths[self.current_file_idx];
        println!("Loading chunk from file {}: {} (position: {})", 
                 self.current_file_idx + 1, file_path, self.current_position_in_file);
        
        // Load chunk with offset and limit
        let df = LazyFrame::scan_parquet(file_path, Default::default())
            .slice(self.current_position_in_file as i64, self.chunk_size as u32)
            .collect()
            .map_err(|e| candle_core::Error::Msg(format!("Failed to load chunk: {}", e)))?;
        
        if df.height() == 0 {
            // End of current file, move to next
            self.current_file_idx += 1;
            self.current_position_in_file = 0;
            return self.load_next_chunk();
        }
        
        // Filter feature columns (exclude timestamp)
        let feature_columns: Vec<String> = df.get_column_names()
            .iter()
            .filter(|&name| !name.to_lowercase().contains("timestamp"))
            .map(|&name| name.to_string())
            .collect();
        
        let filtered_df = df.select(&feature_columns)
            .map_err(|e| candle_core::Error::Msg(format!("Failed to select features: {}", e)))?;
        
        // Convert to tensor
        let num_rows = filtered_df.height();
        let num_cols = filtered_df.width();
        
        // Extract data as f32 vectors (column-major from polars)
        let mut data_matrix = Vec::with_capacity(num_rows * num_cols);
        
        for col_name in &feature_columns {
            let column = filtered_df.column(col_name)
                .map_err(|e| candle_core::Error::Msg(format!("Failed to get column {}: {}", col_name, e)))?;
            
            let values: Vec<f32> = column
                .f32()
                .map_err(|e| candle_core::Error::Msg(format!("Failed to convert column to f32: {}", e)))?
                .into_iter()
                .map(|opt_val| opt_val.unwrap_or(0.0))
                .collect();
            
            data_matrix.extend(values);
        }
        
        // Convert to row-major format
        let mut row_major_data = vec![0.0f32; num_rows * num_cols];
        for row in 0..num_rows {
            for col in 0..num_cols {
                row_major_data[row * num_cols + col] = data_matrix[col * num_rows + row];
            }
        }
        
        // Create tensor on CPU first to avoid GPU memory issues
        let cpu_tensor = Tensor::from_vec(row_major_data, &[num_rows, num_cols], &Device::Cpu)?;
        
        // Only move to GPU when we actually need it for processing
        self.current_data_chunk = Some(cpu_tensor);
        self.current_position_in_file += num_rows;
        
        println!("✅ Loaded chunk: {} timesteps × {} features", num_rows, num_cols);
        Ok(true)
    }
    
    /// Get the next batch of sequences
    pub fn next(&mut self) -> Option<Result<Tensor>> {
        // Ensure we have data loaded
        if self.current_data_chunk.is_none() {
            match self.load_next_chunk() {
                Ok(true) => {}, // Successfully loaded
                Ok(false) => return None, // No more data
                Err(e) => return Some(Err(e)),
            }
        }
        
        let chunk = match &self.current_data_chunk {
            Some(chunk) => chunk,
            None => return None,
        };
        
        let chunk_timesteps = chunk.dims()[0];
        let mut batch_sequences = Vec::new();
        let mut sequences_collected = 0;
        
        // Collect sequences for this batch
        while sequences_collected < self.batch_size {
            // Check if we can create a sequence from current position
            let remaining_in_chunk = chunk_timesteps.saturating_sub(self.current_position_in_file % chunk_timesteps);
            
            if remaining_in_chunk < self.sequence_length {
                // Need to load next chunk
                match self.load_next_chunk() {
                    Ok(true) => continue, // Try again with new chunk
                    Ok(false) => break, // No more data
                    Err(e) => return Some(Err(e)),
                }
            }
            
            // Extract sequence from current chunk
            let start_pos = self.current_position_in_file % chunk_timesteps;
            match chunk.narrow(0, start_pos, self.sequence_length) {
                Ok(sequence) => {
                    // Move sequence to GPU only when needed
                    match sequence.to_device(&self.device) {
                        Ok(gpu_sequence) => batch_sequences.push(gpu_sequence),
                        Err(e) => return Some(Err(e)),
                    }
                    sequences_collected += 1;
                    self.total_sequences_processed += 1;
                },
                Err(e) => return Some(Err(e)),
            }
            
            // Move to next sequence position
            self.current_position_in_file += 1;
        }
        
        if batch_sequences.is_empty() {
            return None;
        }
        
        // Stack sequences into batch
        match Tensor::stack(&batch_sequences, 0) {
            Ok(batch) => {
                println!("Batch created: {:?} (total sequences processed: {})", 
                         batch.shape(), self.total_sequences_processed);
                Some(Ok(batch))
            },
            Err(e) => Some(Err(e)),
        }
    }
    
    /// Reset the batcher to start from the beginning
    pub fn reset(&mut self) {
        self.current_file_idx = 0;
        self.current_position_in_file = 0;
        self.current_data_chunk = None;
        self.total_sequences_processed = 0;
        println!("StreamingBatcher reset to beginning");
    }
    
    /// Get total sequences processed so far
    pub fn sequences_processed(&self) -> usize {
        self.total_sequences_processed
    }
}

/// Iterator implementation for StreamingBatcher
impl Iterator for StreamingBatcher {
    type Item = Result<Tensor>;

    fn next(&mut self) -> Option<Self::Item> {
        self.next()
    }
}
